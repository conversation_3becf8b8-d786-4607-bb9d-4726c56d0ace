<?php if (isset($component)) { $__componentOriginal283b9f021c038c6d2eeddbfd373ad7a9 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal283b9f021c038c6d2eeddbfd373ad7a9 = $attributes; } ?>
<?php $component = App\View\Components\Layouts\UnilinkLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('layouts.unilink-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\Layouts\UnilinkLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
    <div class="space-y-6">
        <!-- Header -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-2xl font-bold text-gray-900">My Groups</h1>
                    <p class="text-gray-600 mt-1">Manage your group memberships and discover new communities</p>
                </div>
                <div class="flex space-x-3">
                    <a href="<?php echo e(route('groups.index')); ?>" class="bg-gray-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-gray-700">
                        Browse Groups
                    </a>
                    <a href="<?php echo e(route('groups.create')); ?>" class="bg-blue-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-blue-700">
                        Create Group
                    </a>
                </div>
            </div>
        </div>

        <!-- Stats Cards -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <div class="flex items-center">
                    <div class="p-3 bg-green-100 rounded-lg">
                        <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Active Groups</p>
                        <p class="text-2xl font-bold text-gray-900"><?php echo e($myGroups->count()); ?></p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <div class="flex items-center">
                    <div class="p-3 bg-yellow-100 rounded-lg">
                        <svg class="w-6 h-6 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Pending Requests</p>
                        <p class="text-2xl font-bold text-gray-900"><?php echo e($pendingGroups->count()); ?></p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <div class="flex items-center">
                    <div class="p-3 bg-blue-100 rounded-lg">
                        <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Groups Created</p>
                        <p class="text-2xl font-bold text-gray-900"><?php echo e(auth()->user()->createdGroups()->count()); ?></p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Pending Requests -->
        <?php if($pendingGroups->count() > 0): ?>
            <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                <div class="p-4 border-b border-gray-200">
                    <h2 class="text-lg font-semibold text-gray-900 flex items-center">
                        <svg class="w-5 h-5 mr-2 text-yellow-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        Pending Requests
                    </h2>
                </div>
                
                <div class="divide-y divide-gray-200">
                    <?php $__currentLoopData = $pendingGroups; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $group): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="p-4">
                            <div class="flex items-center space-x-4">
                                <!-- Group Logo -->
                                <div class="flex-shrink-0">
                                    <div class="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center">
                                        <?php if($group->logo): ?>
                                            <img src="<?php echo e(Storage::disk('public')->url($group->logo)); ?>" alt="<?php echo e($group->name); ?>" class="w-10 h-10 rounded-lg object-cover">
                                        <?php else: ?>
                                            <span class="text-yellow-600 font-bold text-sm"><?php echo e(substr($group->name, 0, 2)); ?></span>
                                        <?php endif; ?>
                                    </div>
                                </div>
                                
                                <!-- Group Info -->
                                <div class="flex-1 min-w-0">
                                    <h3 class="text-lg font-semibold text-gray-900">
                                        <a href="<?php echo e(route('groups.show', $group)); ?>" class="hover:text-blue-600">
                                            <?php echo e($group->name); ?>

                                        </a>
                                    </h3>
                                    <p class="text-gray-600 text-sm"><?php echo e(Str::limit($group->description, 100)); ?></p>
                                    <div class="flex items-center space-x-4 mt-2 text-sm text-gray-500">
                                        <span><?php echo e($group->active_members_count); ?> members</span>
                                        <span><?php echo e(ucfirst($group->visibility)); ?> group</span>
                                        <span>Requested <?php echo e($group->pivot->created_at->diffForHumans()); ?></span>
                                    </div>
                                </div>
                                
                                <!-- Status -->
                                <div class="flex-shrink-0">
                                    <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-yellow-100 text-yellow-800">
                                        Pending Approval
                                    </span>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
            </div>
        <?php endif; ?>

        <!-- My Active Groups -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
            <div class="p-4 border-b border-gray-200">
                <h2 class="text-lg font-semibold text-gray-900 flex items-center">
                    <svg class="w-5 h-5 mr-2 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 515.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                    </svg>
                    My Groups (<?php echo e($myGroups->count()); ?>)
                </h2>
            </div>
            
            <?php if($myGroups->count() > 0): ?>
                <div class="divide-y divide-gray-200">
                    <?php $__currentLoopData = $myGroups; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $group): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="p-4">
                            <div class="flex items-center space-x-4">
                                <!-- Group Logo -->
                                <div class="flex-shrink-0">
                                    <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                                        <?php if($group->logo): ?>
                                            <img src="<?php echo e(Storage::disk('public')->url($group->logo)); ?>" alt="<?php echo e($group->name); ?>" class="w-10 h-10 rounded-lg object-cover">
                                        <?php else: ?>
                                            <span class="text-blue-600 font-bold text-sm"><?php echo e(substr($group->name, 0, 2)); ?></span>
                                        <?php endif; ?>
                                    </div>
                                </div>
                                
                                <!-- Group Info -->
                                <div class="flex-1 min-w-0">
                                    <div class="flex items-center space-x-2">
                                        <h3 class="text-lg font-semibold text-gray-900">
                                            <a href="<?php echo e(route('groups.show', $group)); ?>" class="hover:text-blue-600">
                                                <?php echo e($group->name); ?>

                                            </a>
                                        </h3>
                                        <?php if($group->created_by === auth()->id()): ?>
                                            <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-purple-100 text-purple-800">
                                                Creator
                                            </span>
                                        <?php elseif($group->pivot->role === 'admin'): ?>
                                            <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-red-100 text-red-800">
                                                Admin
                                            </span>
                                        <?php elseif($group->pivot->role === 'moderator'): ?>
                                            <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">
                                                Moderator
                                            </span>
                                        <?php endif; ?>
                                    </div>
                                    <p class="text-gray-600 text-sm"><?php echo e(Str::limit($group->description, 100)); ?></p>
                                    <div class="flex items-center space-x-4 mt-2 text-sm text-gray-500">
                                        <span><?php echo e($group->active_members_count); ?> members</span>
                                        <span><?php echo e(ucfirst($group->visibility)); ?> group</span>
                                        <span>Joined <?php echo e($group->pivot->joined_at ? $group->pivot->joined_at->diffForHumans() : 'recently'); ?></span>
                                        <?php if($group->organization): ?>
                                            <span>• <?php echo e($group->organization->name); ?></span>
                                        <?php endif; ?>
                                    </div>
                                </div>
                                
                                <!-- Actions -->
                                <div class="flex-shrink-0 flex items-center space-x-2">
                                    <?php if($group->userCanModerate(auth()->user())): ?>
                                        <a href="<?php echo e(route('groups.members', $group)); ?>" class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                                            Manage
                                        </a>
                                        <a href="<?php echo e(route('groups.edit', $group)); ?>" class="text-gray-600 hover:text-gray-800 text-sm font-medium">
                                            Edit
                                        </a>
                                    <?php endif; ?>
                                    
                                    <?php if($group->created_by !== auth()->id()): ?>
                                        <?php
$__split = function ($name, $params = []) {
    return [$name, $params];
};
[$__name, $__params] = $__split('group-membership', ['group' => $group]);

$__html = app('livewire')->mount($__name, $__params, 'lw-2536442922-0', $__slots ?? [], get_defined_vars());

echo $__html;

unset($__html);
unset($__name);
unset($__params);
unset($__split);
if (isset($__slots)) unset($__slots);
?>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
            <?php else: ?>
                <div class="p-12 text-center">
                    <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 515.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                    </svg>
                    <h3 class="mt-2 text-sm font-medium text-gray-900">No groups yet</h3>
                    <p class="mt-1 text-sm text-gray-500">Get started by joining a group or creating your own.</p>
                    <div class="mt-6 flex justify-center space-x-3">
                        <a href="<?php echo e(route('groups.index')); ?>" class="bg-blue-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-blue-700">
                            Browse Groups
                        </a>
                        <a href="<?php echo e(route('groups.create')); ?>" class="bg-gray-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-gray-700">
                            Create Group
                        </a>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal283b9f021c038c6d2eeddbfd373ad7a9)): ?>
<?php $attributes = $__attributesOriginal283b9f021c038c6d2eeddbfd373ad7a9; ?>
<?php unset($__attributesOriginal283b9f021c038c6d2eeddbfd373ad7a9); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal283b9f021c038c6d2eeddbfd373ad7a9)): ?>
<?php $component = $__componentOriginal283b9f021c038c6d2eeddbfd373ad7a9; ?>
<?php unset($__componentOriginal283b9f021c038c6d2eeddbfd373ad7a9); ?>
<?php endif; ?>
<?php /**PATH C:\xampp\htdocs\LARAVEL_PROJECTS\lara_unilink\resources\views/groups/my.blade.php ENDPATH**/ ?>