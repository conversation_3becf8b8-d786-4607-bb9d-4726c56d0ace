<?php

namespace App\Http\Controllers;

use App\Http\Requests\ProfileUpdateRequest;
use App\Models\User;
use App\Models\School;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Facades\Storage;
use Illuminate\View\View;

class ProfileController extends Controller
{
    /**
     * Display the user's profile page.
     */
    public function show(Request $request, User $user = null): View
    {
        // If no user specified, show current user's profile
        $user = $user ?? $request->user();

        // Load user relationships
        $user->load([
            'posts' => function ($query) {
                $query->published()
                      ->with(['organization', 'likes', 'comments.user', 'comments.reactions', 'comments.replies.user', 'comments.replies.reactions', 'shares', 'fileAttachments'])
                      ->latest('published_at');
            },
            'shares' => function ($query) use ($user) {
                $query->where('share_type', 'direct')
                      ->with(['likes', 'comments', 'post.user', 'post.organization', 'post.likes', 'post.comments', 'post.shares', 'post.fileAttachments'])
                      ->whereHas('post', function($q) {
                          $q->published();
                      })
                      ->where(function($q) use ($user) {
                          // Show all shares if viewing own profile, otherwise respect privacy
                          if (auth()->check() && auth()->id() === $user->id) {
                              // Own profile - show all shares
                              return;
                          } else {
                              // Other's profile - respect privacy scope
                              $q->where('privacy_scope', 'public')
                                ->orWhere(function($subQ) {
                                    $subQ->where('privacy_scope', 'friends');
                                    // In a real app, check friendship here
                                })
                                ->orWhere(function($subQ) {
                                    $subQ->where('privacy_scope', 'custom');
                                    // In a real app, check custom privacy here
                                });
                          }
                      })
                      ->latest('created_at');
            },
            'activeOrganizations' => function ($query) {
                $query->withPivot(['role', 'joined_at']);
            },
            'createdOrganizations',
            'school',
            'campus'
        ]);

        // Get user statistics
        $stats = [
            'posts_count' => $user->posts()->published()->count(),
            'shares_count' => $user->shares()->where('share_type', 'direct')->whereHas('post', function($q) { $q->published(); })->count(),
            'total_activity_count' => $user->posts()->published()->count() + $user->shares()->where('share_type', 'direct')->whereHas('post', function($q) { $q->published(); })->count(),
            'organizations_count' => $user->activeOrganizations()->count(),
            'created_organizations_count' => $user->createdOrganizations()->count(),
            'total_likes' => $user->posts()->published()->withCount('likes')->get()->sum('likes_count'),
            'total_comments' => $user->posts()->published()->withCount('comments')->get()->sum('comments_count'),
        ];

        // Combine posts and shares for recent activity
        $recentActivity = collect();

        // Add posts
        foreach ($user->posts as $post) {
            $recentActivity->push((object)[
                'type' => 'post',
                'data' => $post,
                'created_at' => $post->published_at,
            ]);
        }

        // Add shares
        foreach ($user->shares as $share) {
            $recentActivity->push((object)[
                'type' => 'share',
                'data' => $share,
                'created_at' => $share->created_at,
            ]);
        }

        // Sort by date (newest first)
        $recentActivity = $recentActivity->sortByDesc('created_at');

        return view('profile.show', compact('user', 'stats', 'recentActivity'));
    }

    /**
     * Display the user's profile form (standalone edit page).
     */
    public function edit(Request $request): View
    {
        $schools = School::active()->with('activeCampuses')->get();

        return view('profile.edit', [
            'user' => $request->user(),
            'schools' => $schools,
        ]);
    }

    /**
     * Update the user's profile information.
     */
    public function update(ProfileUpdateRequest $request): RedirectResponse
    {
        $validated = $request->validated();

        // Handle avatar upload
        if ($request->hasFile('avatar')) {
            // Delete old avatar if exists
            if ($request->user()->avatar) {
                Storage::delete($request->user()->avatar);
            }

            $avatarPath = $request->file('avatar')->store('avatars', 'public');
            $validated['avatar'] = $avatarPath;
        }

        // Handle background photo upload
        if ($request->hasFile('background_photo')) {
            // Delete old background photo if exists
            if ($request->user()->background_photo) {
                Storage::delete($request->user()->background_photo);
            }

            $backgroundPhotoPath = $request->file('background_photo')->store('background_photos', 'public');
            $validated['background_photo'] = $backgroundPhotoPath;
        }

        // Handle profile visibility settings
        if (isset($validated['profile_visibility'])) {
            $profileVisibility = [];
            foreach ($validated['profile_visibility'] as $key => $value) {
                $profileVisibility[$key] = (bool) $value;
            }
            $validated['profile_visibility'] = $profileVisibility;
        }

        // Ensure arrays are properly formatted
        if (isset($validated['skills_interests']) && is_array($validated['skills_interests'])) {
            $validated['skills_interests'] = array_filter($validated['skills_interests']);
        }

        if (isset($validated['achievements']) && is_array($validated['achievements'])) {
            $validated['achievements'] = array_filter($validated['achievements']);
        }

        if (isset($validated['social_links']) && is_array($validated['social_links'])) {
            $socialLinks = [];
            foreach ($validated['social_links'] as $link) {
                if (!empty($link['platform']) && !empty($link['url'])) {
                    $socialLinks[] = $link;
                }
            }
            $validated['social_links'] = $socialLinks;
        }

        $request->user()->fill($validated);

        if ($request->user()->isDirty('email')) {
            $request->user()->email_verified_at = null;
        }

        $request->user()->save();

        return Redirect::route('profile.show')->with('status', 'profile-updated');
    }

    /**
     * Delete the user's background photo.
     */
    public function deleteBackgroundPhoto(Request $request): RedirectResponse
    {
        $user = $request->user();

        if ($user->background_photo) {
            Storage::delete($user->background_photo);
            $user->background_photo = null;
            $user->save();
        }

        return Redirect::route('profile.show')->with('status', 'background-photo-deleted');
    }

    /**
     * Delete the user's account.
     */
    public function destroy(Request $request): RedirectResponse
    {
        $request->validateWithBag('userDeletion', [
            'password' => ['required', 'current_password'],
        ]);

        $user = $request->user();

        Auth::logout();

        $user->delete();

        $request->session()->invalidate();
        $request->session()->regenerateToken();

        return Redirect::to('/');
    }
}
