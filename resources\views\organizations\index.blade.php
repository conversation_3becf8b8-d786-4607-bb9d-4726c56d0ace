<x-layouts.unilink-layout>
    <!-- <PERSON> Header -->
    <div class="mb-8">
        <div class="flex justify-between items-center">
            <div>
                <h1 class="text-2xl font-bold text-gray-900">All Organizations</h1>
                <p class="text-gray-600 mt-1">Discover and join student organizations on campus</p>
            </div>
            @auth
                @if(auth()->user()->isAdmin())
                    <div class="flex space-x-3">
                        <a href="{{ route('organization-requests.index') }}" class="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500">
                            View Requests
                        </a>
                        <a href="{{ route('organizations.create') }}" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
                            Create Organization
                        </a>
                    </div>
                @else
                    <div class="flex space-x-3">
                        <a href="{{ route('organization-requests.index') }}" class="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500">
                            My Requests
                        </a>
                        <a href="{{ route('organization-requests.create') }}" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <svg class="w-4 h-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
                            </svg>
                            Request Organization
                        </a>
                    </div>
                @endif
            @endauth
        </div>
    </div>

    <!-- Search and Filter Bar -->
    <div class="mb-6 bg-white rounded-lg shadow-sm border border-gray-200 p-4">
        <div class="flex flex-col md:flex-row gap-4">
            <!-- Search -->
            <div class="flex-1 relative" id="organization-search-container">
                <div class="relative">
                    <input type="text"
                           id="organization-search-input"
                           placeholder="Search organizations..."
                           class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                        </svg>
                    </div>
                </div>

                <!-- Search Results Dropdown -->
                <div id="organization-search-results"
                     class="absolute top-full left-0 right-0 mt-2 bg-white rounded-lg shadow-lg border border-gray-200 max-h-96 overflow-y-auto z-50 hidden">
                    <div id="organization-search-results-content">
                        <!-- Results will be populated here -->
                    </div>
                    <div id="organization-search-loading" class="hidden p-4 text-center text-gray-500">
                        <svg class="animate-spin h-5 w-5 mx-auto" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        <span class="ml-2">Searching...</span>
                    </div>
                    <div id="organization-search-no-results" class="hidden p-4 text-center text-gray-500">
                        <svg class="h-8 w-8 mx-auto text-gray-400 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.172 16.172a4 4 0 015.656 0M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                        </svg>
                        <p class="text-sm">No organizations found</p>
                    </div>
                </div>
            </div>

            <!-- Category Filter -->
            <div class="md:w-48">
                <select class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                    <option>All Categories</option>
                    <option>Academic</option>
                    <option>Sports</option>
                    <option>Arts & Culture</option>
                    <option>Community Service</option>
                    <option>Professional</option>
                </select>
            </div>
        </div>
    </div>

    <!-- Call to Action for Organization Creation -->
    @auth
        @if(!auth()->user()->isAdmin())
            <div class="mb-6 bg-blue-50 border border-blue-200 rounded-lg p-6">
                <div class="flex items-start space-x-4">
                    <div class="flex-shrink-0">
                        <svg class="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                        </svg>
                    </div>
                    <div class="flex-1">
                        <h3 class="text-lg font-semibold text-blue-900 mb-2">Want to create your own organization?</h3>
                        <p class="text-blue-700 mb-4">If you're the president of a student organization and want to create an official presence on our platform, you can submit a request for review.</p>
                        <div class="flex space-x-3">
                            <a href="{{ route('organization-requests.create') }}" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
                                </svg>
                                Submit Request
                            </a>
                            <a href="{{ route('organization-requests.index') }}" class="inline-flex items-center px-4 py-2 border border-blue-300 text-sm font-medium rounded-md text-blue-700 bg-white hover:bg-blue-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                View My Requests
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        @endif
    @endauth

    <!-- Organizations Grid -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        @forelse($organizations as $organization)
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-shadow">
                <!-- Cover Image -->
                <div class="h-32 bg-gradient-to-r from-blue-500 to-purple-600 relative">
                    @if($organization->cover_image)
                        <img src="{{ \Illuminate\Support\Facades\Storage::disk('public')->url($organization->cover_image) }}" alt="{{ $organization->name }}" class="w-full h-full object-cover">
                    @endif
                    
                    <!-- Logo -->
                    <div class="absolute -bottom-6 left-4">
                        <div class="w-12 h-12 bg-white rounded-lg shadow-md flex items-center justify-center">
                            @if($organization->logo)
                                <img src="{{ \Illuminate\Support\Facades\Storage::disk('public')->url($organization->logo) }}" alt="{{ $organization->name }}" class="w-10 h-10 rounded-lg object-cover">
                            @else
                                <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                                    <span class="text-blue-600 font-semibold text-sm">{{ substr($organization->name, 0, 2) }}</span>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>

                <!-- Content -->
                <div class="p-4 pt-8">
                    <div class="flex justify-between items-start mb-2">
                        <h3 class="text-lg font-semibold text-gray-900 truncate">{{ $organization->name }}</h3>
                        <span class="text-sm text-gray-500">{{ $organization->active_members_count }} members</span>
                    </div>
                    
                    <p class="text-gray-600 text-sm mb-4 line-clamp-2">{{ Str::limit($organization->description, 100) }}</p>
                    
                    <!-- Organization Info -->
                    <div class="flex items-center text-xs text-gray-500 mb-4">
                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                        </svg>
                        Created by {{ $organization->creator->name }}
                    </div>

                    <!-- Actions -->
                    <div class="flex gap-2">
                        <a href="{{ route('organizations.show', $organization) }}" class="flex-1 bg-blue-600 text-white text-center py-2 px-4 rounded-lg text-sm font-medium hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
                            View Details
                        </a>
                        
                        @auth
                            @php
                                $userMembership = $organization->members()->where('user_id', auth()->id())->first();
                            @endphp
                            
                            @if(!$userMembership)
                                <form action="{{ route('organizations.join', $organization) }}" method="POST" class="flex-1">
                                    @csrf
                                    <button type="submit" class="w-full bg-green-600 text-white py-2 px-4 rounded-lg text-sm font-medium hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500">
                                        Join
                                    </button>
                                </form>
                            @elseif($userMembership->pivot->status === 'pending')
                                <button disabled class="flex-1 bg-yellow-100 text-yellow-800 py-2 px-4 rounded-lg text-sm font-medium cursor-not-allowed">
                                    Pending
                                </button>
                            @else
                                <button disabled class="flex-1 bg-gray-100 text-gray-600 py-2 px-4 rounded-lg text-sm font-medium cursor-not-allowed">
                                    Member
                                </button>
                            @endif
                        @endauth
                    </div>
                </div>
            </div>
        @empty
            <div class="col-span-full text-center py-12">
                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                </svg>
                <h3 class="mt-2 text-sm font-medium text-gray-900">No organizations found</h3>
                <p class="mt-1 text-sm text-gray-500">Get started by creating the first organization.</p>
                @if(auth()->user()->hasManagementAccess())
                    <div class="mt-6">
                        <a href="{{ route('organizations.create') }}" class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            Create Organization
                        </a>
                    </div>
                @endif
            </div>
        @endforelse
    </div>

    <!-- Pagination -->
    @if($organizations->hasPages())
        <div class="mt-8">
            {{ $organizations->links() }}
        </div>
    @endif

    <!-- Organization Search JavaScript -->
    <script>
    document.addEventListener('DOMContentLoaded', function() {
        const searchInput = document.getElementById('organization-search-input');
        const searchResults = document.getElementById('organization-search-results');
        const searchContent = document.getElementById('organization-search-results-content');
        const searchLoading = document.getElementById('organization-search-loading');
        const searchNoResults = document.getElementById('organization-search-no-results');
        let searchTimeout;

        if (searchInput) {
            // Handle search input
            searchInput.addEventListener('input', function() {
                const query = this.value.trim();

                clearTimeout(searchTimeout);

                if (query.length === 0) {
                    hideSearchResults();
                    return;
                }

                // Show loading state
                showSearchResults();
                showLoading();

                // Dynamic search with minimal delay
                const delay = query.length === 1 ? 100 : 50;
                searchTimeout = setTimeout(() => {
                    performSearch(query);
                }, delay);
            });

            // Handle focus and blur events
            searchInput.addEventListener('focus', function() {
                if (this.value.trim().length > 0) {
                    showSearchResults();
                }
            });

            // Hide results when clicking outside
            document.addEventListener('click', function(e) {
                if (!document.getElementById('organization-search-container').contains(e.target)) {
                    hideSearchResults();
                }
            });

            // Handle keyboard navigation
            searchInput.addEventListener('keydown', function(e) {
                if (e.key === 'Escape') {
                    hideSearchResults();
                    this.blur();
                }
            });
        }

        function performSearch(query) {
            fetch(`/api/organizations/search?q=${encodeURIComponent(query)}`, {
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'Accept': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                hideLoading();

                if (data.success && data.organizations.length > 0) {
                    displaySearchResults(data.organizations);
                } else {
                    showNoResults();
                }
            })
            .catch(error => {
                console.error('Search error:', error);
                hideLoading();
                showNoResults();
            });
        }

        function displaySearchResults(organizations) {
            searchContent.innerHTML = organizations.map(org => {
                const logoHtml = org.logo_url
                    ? `<img src="${org.logo_url}" alt="${org.name}" class="w-10 h-10 rounded-lg object-cover">`
                    : `<div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                         <span class="text-blue-600 font-semibold text-sm">${org.name.substring(0, 2)}</span>
                       </div>`;

                const membershipStatus = getMembershipStatus(org.user_membership);

                return `
                    <div class="flex items-center p-3 hover:bg-gray-50 transition-colors border-b border-gray-100 last:border-b-0">
                        <div class="flex-shrink-0 mr-3">
                            ${logoHtml}
                        </div>
                        <div class="flex-1 min-w-0">
                            <div class="font-medium text-gray-900 truncate">${org.name}</div>
                            <div class="text-sm text-gray-500 truncate">
                                ${org.active_members_count} members • Created by ${org.creator_name}
                            </div>
                            ${org.description ? `<div class="text-xs text-gray-400 truncate mt-1">${org.description}</div>` : ''}
                        </div>
                        <div class="flex-shrink-0 ml-3">
                            <a href="${org.url}" class="bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700">
                                View
                            </a>
                        </div>
                    </div>
                `;
            }).join('');

            searchContent.classList.remove('hidden');
            searchNoResults.classList.add('hidden');
        }

        function getMembershipStatus(membership) {
            if (!membership) return '';
            if (membership.pivot && membership.pivot.status === 'pending') return 'Pending';
            if (membership.pivot && membership.pivot.status === 'active') return 'Member';
            return '';
        }

        function showSearchResults() {
            searchResults.classList.remove('hidden');
        }

        function hideSearchResults() {
            searchResults.classList.add('hidden');
        }

        function showLoading() {
            searchLoading.classList.remove('hidden');
            searchContent.classList.add('hidden');
            searchNoResults.classList.add('hidden');
        }

        function hideLoading() {
            searchLoading.classList.add('hidden');
        }

        function showNoResults() {
            searchNoResults.classList.remove('hidden');
            searchContent.classList.add('hidden');
        }
    });
    </script>
</x-layouts.unilink-layout>
